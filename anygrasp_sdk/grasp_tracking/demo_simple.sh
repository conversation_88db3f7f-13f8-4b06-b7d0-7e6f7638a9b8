#!/bin/bash

# AnyGrasp Tracking Simple Demo Script
# This script runs the simplified demo with reliable 3D visualization

echo "=========================================="
echo "AnyGrasp Tracking - Simple Demo"
echo "=========================================="

# Check if checkpoint exists
CHECKPOINT_PATH="log/checkpoint_tracking.tar"
if [ ! -f "$CHECKPOINT_PATH" ]; then
    echo "Error: Checkpoint file '$CHECKPOINT_PATH' not found!"
    echo "Please make sure you have downloaded the model weights."
    exit 1
fi

# Check if example data exists
DATA_DIR="example_data"
if [ ! -d "$DATA_DIR" ]; then
    echo "Error: Data directory '$DATA_DIR' not found!"
    echo "Please make sure you have the example data."
    exit 1
fi

echo "Starting simple demo with reliable 3D visualization..."
echo "Instructions:"
echo "- Each frame will show in a separate window"
echo "- Close the window to proceed to the next frame"
echo "- Press Ctrl+C to stop the demo"
echo ""

# Run the simple demo
python demo_simple.py \
    --checkpoint_path "$CHECKPOINT_PATH" \
    --filter oneeuro \
    --debug

echo ""
echo "Demo completed!"
