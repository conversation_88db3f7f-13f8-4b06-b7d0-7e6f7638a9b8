# AnyGrasp Tracking - Enhanced Demo

这是一个优化版本的AnyGrasp跟踪演示程序，主要改进了3D可视化功能。

## 🚀 主要改进

### 1. **增强的3D可视化**
- ✅ 修复了原版demo.py中3D可视化窗口空白的问题
- ✅ 添加了坐标系显示，便于理解空间关系
- ✅ 优化了相机视角和渲染设置
- ✅ 支持抓取置信度的颜色编码（红色=高置信度，蓝色=低置信度）
- ✅ 实时更新显示，支持连续帧可视化

### 2. **用户友好的界面**
- ✅ 详细的使用说明和参数解释
- ✅ 自动验证数据文件和模型文件
- ✅ 更好的错误处理和用户提示
- ✅ 支持保存可视化帧为图片

### 3. **代码结构优化**
- ✅ 模块化的可视化类设计
- ✅ 更清晰的代码注释和文档
- ✅ 遵循Python代码规范

## 📋 使用方法

### 快速开始
```bash
# 使用脚本运行（推荐）
chmod +x demo_user.sh
./demo_user.sh

# 或者直接运行Python文件
python demo_user.py --checkpoint_path log/checkpoint_tracking.tar --debug
```

### 命令行参数
```bash
python demo_user.py [OPTIONS]

必需参数:
  --checkpoint_path PATH    模型权重文件路径

可选参数:
  --filter TYPE            平滑滤波器类型 [oneeuro/kalman/none]
                          默认: oneeuro
  --debug                  启用3D可视化
  --save_frames           保存可视化帧为PNG图片
```

## 🎮 可视化控制

### 鼠标操作
- **左键拖拽**: 旋转视角
- **右键拖拽**: 平移视角  
- **滚轮**: 缩放
- **ESC**: 关闭可视化窗口

### 颜色编码
- 🔴 **红色抓取**: 高置信度抓取点
- 🔵 **蓝色抓取**: 低置信度抓取点
- ⚪ **白色坐标系**: 世界坐标原点
- 🌈 **彩色点云**: RGB颜色对应的3D点

## 📁 文件结构

```
anygrasp_sdk/grasp_tracking/
├── demo.py              # 原始演示文件
├── demo_user.py         # 优化版演示文件 ⭐
├── demo_user.sh         # 运行脚本 ⭐
├── README_USER.md       # 使用说明 ⭐
├── tracker.so           # 跟踪库
├── lib_cxx.so          # C++库
├── log/                 # 模型权重目录
│   └── checkpoint_tracking.tar
├── example_data/        # 示例数据
│   ├── color_000.png
│   ├── depth_000.npy
│   └── ...
└── license/            # 许可证文件
```

## 🔧 环境要求

- Python >= 3.6, <= 3.9
- Open3D >= 0.8
- NumPy
- PIL/Pillow
- graspnetAPI

## 🐛 故障排除

### 问题1: 可视化窗口空白
**解决方案**: 使用 `demo_user.py` 替代原始的 `demo.py`

### 问题2: 找不到模型文件
```bash
Error: Checkpoint file 'log/checkpoint_tracking.tar' not found!
```
**解决方案**: 确保模型权重文件已下载到 `log/` 目录

### 问题3: 找不到数据文件
```bash
Error: Data directory 'example_data' does not exist!
```
**解决方案**: 确保示例数据存在于 `example_data/` 目录

### 问题4: 库文件版本不匹配
**解决方案**: 根据Python版本复制对应的.so文件
```bash
# 例如Python 3.8
cp tracker_versions/tracker.cpython-38-x86_64-linux-gnu.so tracker.so
cp ../license_registration/lib_cxx_versions/lib_cxx.cpython-38-x86_64-linux-gnu.so lib_cxx.so
```

## 📊 性能优化

- 使用 `--filter oneeuro` 获得最平滑的跟踪效果
- 限制显示的抓取数量（默认最多10个）以提高性能
- 添加适当的延迟以便观察可视化效果

## 🎯 技术特点

1. **实时跟踪**: 在连续帧中跟踪特定抓取点
2. **空间筛选**: 智能选择工作空间内的抓取点
3. **平滑滤波**: 减少预测抖动
4. **3D可视化**: 实时显示点云和抓取姿态
5. **模块化设计**: 易于扩展和修改

## 📝 更新日志

### v1.0 (当前版本)
- ✅ 修复3D可视化显示问题
- ✅ 添加增强的可视化类
- ✅ 改进用户界面和错误处理
- ✅ 添加详细文档和使用说明

---

**注意**: 这个优化版本完全兼容原始的AnyGrasp跟踪SDK，只是改进了可视化和用户体验。
