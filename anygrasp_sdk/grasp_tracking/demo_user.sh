#!/bin/bash

# AnyGrasp Tracking Enhanced Demo Script
# This script runs the enhanced demo with improved 3D visualization

echo "=========================================="
echo "AnyGrasp Tracking - Enhanced Demo"
echo "=========================================="

# Check if checkpoint exists
CHECKPOINT_PATH="log/checkpoint_tracking.tar"
if [ ! -f "$CHECKPOINT_PATH" ]; then
    echo "Error: Checkpoint file '$CHECKPOINT_PATH' not found!"
    echo "Please make sure you have downloaded the model weights."
    exit 1
fi

# Check if example data exists
DATA_DIR="example_data"
if [ ! -d "$DATA_DIR" ]; then
    echo "Error: Data directory '$DATA_DIR' not found!"
    echo "Please make sure you have the example data."
    exit 1
fi

# Run the enhanced demo with visualization
echo "Starting enhanced demo with 3D visualization..."
echo "Press Ctrl+C to stop the demo"
echo ""

python demo_user.py \
    --checkpoint_path "$CHECKPOINT_PATH" \
    --filter oneeuro \
    --debug \
    --save_frames

echo ""
echo "Demo completed!"