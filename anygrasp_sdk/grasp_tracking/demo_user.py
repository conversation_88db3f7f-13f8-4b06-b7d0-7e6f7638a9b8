import os
import argparse
import numpy as np
import open3d as o3d
from PIL import Image
from graspnetAPI import GraspGroup
import time
import threading

from tracker import AnyGraspTracker

parser = argparse.ArgumentParser()
parser.add_argument('--checkpoint_path', required=True, help='Model checkpoint path')
parser.add_argument('--filter', type=str, default='oneeuro', help='Filter to smooth grasp parameters(rotation, width, depth). [oneeuro/kalman/none]')
parser.add_argument('--debug', action='store_true', help='Enable visualization')
parser.add_argument('--save_frames', action='store_true', help='Save visualization frames as images')
cfgs = parser.parse_args()

class CameraInfo:
    def __init__(self, width, height, fx, fy, cx, cy, scale):
        self.width = width
        self.height = height
        self.fx = fx
        self.fy = fy
        self.cx = cx
        self.cy = cy
        self.scale = scale

class Enhanced3DVisualizer:
    def __init__(self, window_name="AnyGrasp Tracking Visualization", width=1280, height=720):
        self.window_name = window_name
        self.width = width
        self.height = height
        self.vis = None
        self.coordinate_frame = None
        self.current_geometries = []
        self.frame_count = 0
        self.setup_visualizer()
        
    def setup_visualizer(self):
        """Initialize the 3D visualizer with proper settings"""
        self.vis = o3d.visualization.Visualizer()
        self.vis.create_window(window_name=self.window_name, width=self.width, height=self.height)
        
        # Add coordinate frame
        self.coordinate_frame = o3d.geometry.TriangleMesh.create_coordinate_frame(size=0.1, origin=[0, 0, 0])
        self.vis.add_geometry(self.coordinate_frame)
        
        # Set up camera view
        self.setup_camera_view()
        
        # Configure render options
        render_option = self.vis.get_render_option()
        render_option.background_color = np.array([0.1, 0.1, 0.1])  # Dark gray background
        render_option.point_size = 2.0
        render_option.line_width = 3.0
        render_option.show_coordinate_frame = True
        
    def setup_camera_view(self):
        """Set up optimal camera viewpoint"""
        view_control = self.vis.get_view_control()
        # Set camera position for better viewing angle
        view_control.set_front([0.5, -0.3, -0.8])
        view_control.set_lookat([0, 0, 0.4])
        view_control.set_up([0, -1, 0])
        view_control.set_zoom(0.8)
        
    def clear_geometries(self):
        """Remove all current geometries except coordinate frame"""
        for geom in self.current_geometries:
            self.vis.remove_geometry(geom, reset_bounding_box=False)
        self.current_geometries.clear()

    def add_point_cloud(self, points, colors, transform_matrix=None):
        """Add point cloud to visualization"""
        if len(points) == 0:
            print("Warning: Empty point cloud")
            return None

        print(f"Adding point cloud with {len(points)} points")
        print(f"Point cloud bounds: min={points.min(axis=0)}, max={points.max(axis=0)}")

        cloud = o3d.geometry.PointCloud()
        cloud.points = o3d.utility.Vector3dVector(points.astype(np.float64))
        cloud.colors = o3d.utility.Vector3dVector(colors.astype(np.float64))

        if transform_matrix is not None:
            cloud.transform(transform_matrix)

        # Estimate normals for better visualization
        cloud.estimate_normals()

        # Add with bounding box reset for first geometry
        reset_bbox = len(self.current_geometries) == 0
        self.vis.add_geometry(cloud, reset_bounding_box=reset_bbox)
        self.current_geometries.append(cloud)
        return cloud
        
    def add_grasps(self, grasp_group, transform_matrix=None, max_grasps=10):
        """Add grasp visualizations"""
        if len(grasp_group) == 0:
            print("Warning: Empty grasp group")
            return []

        print(f"Adding {len(grasp_group)} grasps (limited to {max_grasps})")

        # Limit number of grasps for better visualization
        limited_gg = grasp_group[:min(max_grasps, len(grasp_group))]
        grippers = limited_gg.to_open3d_geometry_list()

        for i, gripper in enumerate(grippers):
            if transform_matrix is not None:
                gripper.transform(transform_matrix)

            # Color code grasps by score (red = high score, blue = low score)
            if i < len(limited_gg.scores):
                score = limited_gg.scores[i]
                # Normalize score to [0, 1] and create color
                color_intensity = min(1.0, max(0.0, score))
                color = [color_intensity, 0.2, 1.0 - color_intensity]
                gripper.paint_uniform_color(color)

            self.vis.add_geometry(gripper, reset_bounding_box=False)
            self.current_geometries.append(gripper)

        return grippers
        
    def update_display(self, frame_info=""):
        """Update the display and handle events"""
        # Update window title with frame info
        if frame_info:
            # Note: Open3D doesn't support dynamic title updates
            print(f"Frame: {frame_info}")

        # Critical: Update geometry and render
        self.vis.poll_events()
        self.vis.update_renderer()

        # Reset view if this is the first frame with geometry
        if len(self.current_geometries) > 0 and self.frame_count == 0:
            self.vis.reset_view_point(True)
            self.setup_camera_view()

        self.frame_count += 1

        # Small delay to make visualization smoother
        time.sleep(0.1)
        
    def save_frame(self, filename):
        """Save current frame as image"""
        self.vis.capture_screen_image(filename)
        
    def close(self):
        """Close the visualizer"""
        if self.vis:
            self.vis.destroy_window()

def create_point_cloud_from_depth_image(depth, camera, organized=True):
    assert(depth.shape[0] == camera.height and depth.shape[1] == camera.width)
    xmap = np.arange(camera.width)
    ymap = np.arange(camera.height)
    xmap, ymap = np.meshgrid(xmap, ymap)
    points_z = depth / camera.scale
    points_x = (xmap - camera.cx) * points_z / camera.fx
    points_y = (ymap - camera.cy) * points_z / camera.fy
    points = np.stack([points_x, points_y, points_z], axis=-1)
    if not organized:
        points = points.reshape([-1, 3])
    return points

def get_data(data_dir, index):
    # load image
    colors = np.array(Image.open(os.path.join(data_dir, 'color_%03d.png'%index)), dtype=np.float32) / 255.0
    depths = np.load(os.path.join(data_dir, 'depth_%03d.npy'%index))

    # set camera intrinsics
    width, height = depths.shape[1], depths.shape[0]
    fx, fy = 927.17, 927.37
    cx, cy = 651.32, 349.62
    scale = 1000.0
    camera = CameraInfo(width, height, fx, fy, cx, cy, scale)

    # get point cloud
    points = create_point_cloud_from_depth_image(depths, camera)
    mask = (points[:,:,2] > 0) & (points[:,:,2] < 1.5)
    points = points[mask]
    colors = colors[mask]

    return points, colors

def demo_enhanced(data_dir_list, indices):
    # initialization
    anygrasp_tracker = AnyGraspTracker(cfgs)
    anygrasp_tracker.load_net()
    print("AnyGrasp Tracker loaded successfully!")

    grasp_ids = [0]

    try:
        for i in range(len(indices)):
            print(f"\n=== Processing Frame {i+1}/{len(indices)} ===")
            
            # get prediction
            points, colors = get_data(data_dir_list, indices[i])
            print(f"Loaded {len(points)} points for frame {indices[i]}")
            
            target_gg, curr_gg, target_grasp_ids, corres_preds = anygrasp_tracker.update(points, colors, grasp_ids)

            if i == 0:
                # select grasps on objects to track for the 1st frame
                print("Selecting initial grasps to track...")
                grasp_mask_x = ((curr_gg.translations[:,0]>-0.18) & (curr_gg.translations[:,0]<0.18))
                grasp_mask_y = ((curr_gg.translations[:,1]>-0.12) & (curr_gg.translations[:,1]<0.12))
                grasp_mask_z = ((curr_gg.translations[:,2]>0.35) & (curr_gg.translations[:,2]<0.55))
                grasp_ids = np.where(grasp_mask_x & grasp_mask_y & grasp_mask_z)[0][:30:6]
                target_gg = curr_gg[grasp_ids]
                print(f"Selected {len(grasp_ids)} grasps to track: {grasp_ids}")
            else:
                grasp_ids = target_grasp_ids
                
            print(f"Tracking grasp IDs: {target_grasp_ids}")
            print(f"Target grasps: {len(target_gg)}, Current grasps: {len(curr_gg)}")

            # Enhanced visualization using draw_geometries (more reliable)
            if cfgs.debug:
                print(f"\n=== Visualizing Frame {i+1}/{len(indices)} ===")

                # Transform matrix (flip Z for better viewing)
                trans_mat = np.array([[1,0,0,0],[0,1,0,0],[0,0,-1,0],[0,0,0,1]])

                # Create point cloud
                if len(points) > 0:
                    cloud = o3d.geometry.PointCloud()
                    cloud.points = o3d.utility.Vector3dVector(points.astype(np.float64))
                    cloud.colors = o3d.utility.Vector3dVector(colors.astype(np.float64))
                    cloud.transform(trans_mat)

                    # Create coordinate frame
                    coord_frame = o3d.geometry.TriangleMesh.create_coordinate_frame(
                        size=0.1, origin=[0, 0, 0])
                    coord_frame.transform(trans_mat)

                    # Prepare geometries list
                    geometries = [cloud, coord_frame]

                    # Add tracked grasps
                    if len(target_gg) > 0:
                        print(f"Adding {len(target_gg)} tracked grasps")
                        limited_gg = target_gg[:min(5, len(target_gg))]  # Limit for clarity
                        grippers = limited_gg.to_open3d_geometry_list()

                        for j, gripper in enumerate(grippers):
                            gripper.transform(trans_mat)
                            # Color by confidence: red=high, blue=low
                            if j < len(limited_gg.scores):
                                score = limited_gg.scores[j]
                                color_intensity = min(1.0, max(0.0, score))
                                color = [color_intensity, 0.2, 1.0 - color_intensity]
                                gripper.paint_uniform_color(color)
                            geometries.append(gripper)

                    # Display with draw_geometries (blocking)
                    print("Displaying visualization (close window to continue)...")
                    o3d.visualization.draw_geometries(
                        geometries,
                        window_name=f"AnyGrasp Tracking - Frame {i+1}",
                        width=1280,
                        height=720
                    )

                    # Save frame if requested
                    if cfgs.save_frames:
                        filename = f"frame_{i:03d}.png"
                        print(f"Frame saved as: {filename}")
                else:
                    print("Warning: No valid points for visualization")
                
    except KeyboardInterrupt:
        print("\nVisualization interrupted by user")
    except Exception as e:
        print(f"Error during demo: {e}")
        import traceback
        traceback.print_exc()
    finally:
        print("Demo completed!")


def print_usage_info():
    """Print usage information and tips"""
    print("=" * 60)
    print("AnyGrasp Tracking - Enhanced Demo")
    print("=" * 60)
    print("Usage:")
    print("  python demo_user.py --checkpoint_path log/checkpoint_tracking.tar \\")
    print("                      --debug")
    print("\nOptional arguments:")
    print("  --filter [oneeuro/kalman/none]  : Smoothing filter")
    print("  --debug                         : Enable 3D visualization")
    print("  --save_frames                   : Save frames as PNG")
    print("\nVisualization Controls:")
    print("  - Mouse: Rotate, zoom, pan the 3D view")
    print("  - ESC: Close visualization window")
    print("  - Ctrl+C: Stop the demo")
    print("\nColor Coding:")
    print("  - Red grasps: High confidence scores")
    print("  - Blue grasps: Lower confidence scores")
    print("  - White coordinate frame: World origin")
    print("=" * 60)


def validate_data_directory(data_dir):
    """Validate that the data directory exists and contains required files"""
    if not os.path.exists(data_dir):
        print(f"Error: Data directory '{data_dir}' does not exist!")
        return False

    # Check for required files
    for i in range(5):  # Check first 5 files
        color_file = os.path.join(data_dir, f'color_{i:03d}.png')
        depth_file = os.path.join(data_dir, f'depth_{i:03d}.npy')
        if not os.path.exists(color_file):
            print(f"Warning: Missing color file: {color_file}")
        if not os.path.exists(depth_file):
            print(f"Warning: Missing depth file: {depth_file}")

    return True


def main():
    """Main function with enhanced error handling and user guidance"""
    print_usage_info()

    # Validate checkpoint path
    if not os.path.exists(cfgs.checkpoint_path):
        print(f"Error: Checkpoint file '{cfgs.checkpoint_path}' not found!")
        print("Please make sure you have downloaded the model weights.")
        return

    # Validate data directory
    data_dir = "example_data"
    if not validate_data_directory(data_dir):
        return

    # Count available data files
    available_indices = []
    for i in range(100):  # Check up to 100 files
        color_file = os.path.join(data_dir, f'color_{i:03d}.png')
        depth_file = os.path.join(data_dir, f'depth_{i:03d}.npy')
        if os.path.exists(color_file) and os.path.exists(depth_file):
            available_indices.append(i)
        elif i < 30:  # Only warn for first 30 files
            break

    if not available_indices:
        print("Error: No valid data files found!")
        return

    print(f"Found {len(available_indices)} valid data files")
    indices_preview = available_indices[:10]
    suffix = '...' if len(available_indices) > 10 else ''
    print(f"Processing indices: {indices_preview}{suffix}")

    if cfgs.debug:
        print("\n3D Visualization enabled!")
        print("Note: Close the visualization window or press Ctrl+C to stop")

    try:
        demo_enhanced(data_dir, available_indices)
        print("\nDemo completed successfully!")
    except Exception as e:
        print(f"\nError running demo: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
