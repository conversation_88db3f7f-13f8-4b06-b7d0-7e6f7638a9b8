import os
import argparse
import numpy as np
import open3d as o3d
from PIL import Image
from graspnetAPI import GraspGroup
import time

from tracker import AnyGraspTracker

parser = argparse.ArgumentParser()
parser.add_argument('--checkpoint_path', required=True, help='Model checkpoint path')
parser.add_argument('--filter', type=str, default='oneeuro', help='Filter to smooth grasp parameters(rotation, width, depth). [oneeuro/kalman/none]')
parser.add_argument('--debug', action='store_true', help='Enable visualization')
parser.add_argument('--save_frames', action='store_true', help='Save visualization frames as images')
cfgs = parser.parse_args()

class CameraInfo:
    def __init__(self, width, height, fx, fy, cx, cy, scale):
        self.width = width
        self.height = height
        self.fx = fx
        self.fy = fy
        self.cx = cx
        self.cy = cy
        self.scale = scale

def create_point_cloud_from_depth_image(depth, camera, organized=True):
    assert(depth.shape[0] == camera.height and depth.shape[1] == camera.width)
    xmap = np.arange(camera.width)
    ymap = np.arange(camera.height)
    xmap, ymap = np.meshgrid(xmap, ymap)
    points_z = depth / camera.scale
    points_x = (xmap - camera.cx) * points_z / camera.fx
    points_y = (ymap - camera.cy) * points_z / camera.fy
    points = np.stack([points_x, points_y, points_z], axis=-1)
    if not organized:
        points = points.reshape([-1, 3])
    return points

def get_data(data_dir, index):
    # load image
    colors = np.array(Image.open(os.path.join(data_dir, 'color_%03d.png'%index)), dtype=np.float32) / 255.0
    depths = np.load(os.path.join(data_dir, 'depth_%03d.npy'%index))

    # set camera intrinsics
    width, height = depths.shape[1], depths.shape[0]
    fx, fy = 927.17, 927.37
    cx, cy = 651.32, 349.62
    scale = 1000.0
    camera = CameraInfo(width, height, fx, fy, cx, cy, scale)

    # get point cloud
    points = create_point_cloud_from_depth_image(depths, camera)
    mask = (points[:,:,2] > 0) & (points[:,:,2] < 1.5)
    points = points[mask]
    colors = colors[mask]

    return points, colors

def visualize_frame(points, colors, target_gg, frame_num, total_frames):
    """Simple and reliable visualization using draw_geometries"""
    if len(points) == 0:
        print(f"Frame {frame_num}: No valid points to visualize")
        return
        
    print(f"Visualizing Frame {frame_num}/{total_frames} with {len(points)} points")
    
    # Create point cloud
    cloud = o3d.geometry.PointCloud()
    cloud.points = o3d.utility.Vector3dVector(points.astype(np.float64))
    cloud.colors = o3d.utility.Vector3dVector(colors.astype(np.float64))
    
    # Transform matrix (flip Z for better viewing)
    trans_mat = np.array([[1, 0, 0, 0],
                         [0, 1, 0, 0], 
                         [0, 0, -1, 0],
                         [0, 0, 0, 1]])
    cloud.transform(trans_mat)
    
    # Create coordinate frame
    coord_frame = o3d.geometry.TriangleMesh.create_coordinate_frame(size=0.1, origin=[0, 0, 0])
    coord_frame.transform(trans_mat)
    
    # Prepare geometries
    geometries = [cloud, coord_frame]
    
    # Add grasps if available
    if len(target_gg) > 0:
        print(f"Adding {len(target_gg)} tracked grasps")
        # Limit to 5 grasps for clarity
        limited_gg = target_gg[:min(5, len(target_gg))]
        grippers = limited_gg.to_open3d_geometry_list()
        
        for i, gripper in enumerate(grippers):
            gripper.transform(trans_mat)
            # Color by confidence: red=high, blue=low
            if i < len(limited_gg.scores):
                score = limited_gg.scores[i]
                color_intensity = min(1.0, max(0.0, score))
                color = [color_intensity, 0.2, 1.0 - color_intensity]
                gripper.paint_uniform_color(color)
            geometries.append(gripper)
    
    # Display visualization
    print("Displaying visualization (close window to continue to next frame)...")
    o3d.visualization.draw_geometries(
        geometries,
        window_name=f"AnyGrasp Tracking - Frame {frame_num}/{total_frames}",
        width=1280,
        height=720
    )

def demo_simple(data_dir, indices):
    """Simplified demo with reliable visualization"""
    print("=" * 60)
    print("AnyGrasp Tracking - Simple Demo")
    print("=" * 60)
    
    # Initialize tracker
    anygrasp_tracker = AnyGraspTracker(cfgs)
    anygrasp_tracker.load_net()
    print("AnyGrasp Tracker loaded successfully!")
    
    grasp_ids = [0]
    
    try:
        for i in range(len(indices)):
            print(f"\n=== Processing Frame {i+1}/{len(indices)} ===")
            
            # Get data
            points, colors = get_data(data_dir, indices[i])
            print(f"Loaded {len(points)} points for frame {indices[i]}")
            
            if len(points) == 0:
                print("Warning: No valid points in this frame, skipping...")
                continue
            
            # Update tracker
            target_gg, curr_gg, target_grasp_ids, corres_preds = anygrasp_tracker.update(points, colors, grasp_ids)
            
            if i == 0:
                # Select initial grasps to track
                print("Selecting initial grasps to track...")
                grasp_mask_x = ((curr_gg.translations[:, 0] > -0.18) & (curr_gg.translations[:, 0] < 0.18))
                grasp_mask_y = ((curr_gg.translations[:, 1] > -0.12) & (curr_gg.translations[:, 1] < 0.12))
                grasp_mask_z = ((curr_gg.translations[:, 2] > 0.35) & (curr_gg.translations[:, 2] < 0.55))
                grasp_ids = np.where(grasp_mask_x & grasp_mask_y & grasp_mask_z)[0][:30:6]
                target_gg = curr_gg[grasp_ids]
                print(f"Selected {len(grasp_ids)} grasps to track: {grasp_ids}")
            else:
                grasp_ids = target_grasp_ids
            
            print(f"Tracking grasp IDs: {target_grasp_ids}")
            print(f"Target grasps: {len(target_gg)}, Current grasps: {len(curr_gg)}")
            
            # Visualization
            if cfgs.debug:
                visualize_frame(points, colors, target_gg, i+1, len(indices))
            
            # Save frame info
            if cfgs.save_frames:
                print(f"Frame {i+1} info saved")
                
    except KeyboardInterrupt:
        print("\nDemo interrupted by user")
    except Exception as e:
        print(f"Error during demo: {e}")
        import traceback
        traceback.print_exc()
    
    print("\nDemo completed successfully!")

def main():
    """Main function"""
    print("AnyGrasp Tracking - Simple Demo")
    print("Close each visualization window to proceed to the next frame")
    print("Press Ctrl+C to stop the demo\n")
    
    # Validate checkpoint
    if not os.path.exists(cfgs.checkpoint_path):
        print(f"Error: Checkpoint file '{cfgs.checkpoint_path}' not found!")
        return
    
    # Validate data directory
    data_dir = "example_data"
    if not os.path.exists(data_dir):
        print(f"Error: Data directory '{data_dir}' not found!")
        return
    
    # Find available data files
    available_indices = []
    for i in range(30):
        color_file = os.path.join(data_dir, f'color_{i:03d}.png')
        depth_file = os.path.join(data_dir, f'depth_{i:03d}.npy')
        if os.path.exists(color_file) and os.path.exists(depth_file):
            available_indices.append(i)
    
    if not available_indices:
        print("Error: No valid data files found!")
        return
    
    print(f"Found {len(available_indices)} valid data files")
    print(f"Processing indices: {available_indices}")
    
    # Run demo
    demo_simple(data_dir, available_indices)

if __name__ == "__main__":
    main()
